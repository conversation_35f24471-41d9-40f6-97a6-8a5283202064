import Jetson.GPIO as GPIO
import time

# Pin Definitions
output_pin = 12  # Use GPIO 12 (Pin 32 on the header)

def setup():
    GPIO.setmode(GPIO.BCM)  # or GPIO.BOARD for physical pin numbers
    GPIO.setup(output_pin, GPIO.OUT)

    # Create PWM instance at 50Hz
    pwm = GPIO.PWM(output_pin, 50)  # 50 Hz (20ms period)
    pwm.start(0)  # Start with 0% duty cycle
    return pwm

def set_angle(pwm, angle):
    duty_cycle = 2.5 + (angle / 180.0) * 10  # Maps angle 0-180 to 2.5-12.5%
    pwm.ChangeDutyCycle(duty_cycle)
    time.sleep(0.3)  # Allow servo to reach position
    pwm.ChangeDutyCycle(0)  # Stop signal to prevent jitter

def main():
    pwm = setup()
    try:
        while True:
            angle = float(input("Enter angle (0 to 180 degrees): "))
            if 0 <= angle <= 180:
                set_angle(pwm, angle)
            else:
                print("Please enter a value between 0 and 180.")
    except KeyboardInterrupt:
        pass
    finally:
        pwm.stop()
        GPIO.cleanup()

if __name__ == '__main__':
    main()