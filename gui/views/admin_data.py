"""
Admin data overview view showing all system data.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN
from gui.services.statistics_service import get_attendance_statistics, get_quiz_statistics, get_filter_options
from gui.components.charts import (
    create_attendance_line_chart,
    create_quiz_score_line_chart, create_quiz_score_distribution_pie_chart,
    create_chart_container, create_percentage_card, create_compact_stat_card
)

def create_admin_data_view(page: ft.Page):
    """Create the admin data overview view with integrated statistics."""

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/data", controls=[])

    # State for show/hide functionality
    stats_visible = ft.Ref[ft.Container]()
    attendance_stats_visible = ft.Ref[ft.Container]()
    quiz_stats_visible = ft.Ref[ft.Container]()
    data_tables_visible = ft.Ref[ft.Container]()

    # State for data refresh
    all_data = ft.Ref[dict]()

    # Filter controls
    selected_days = ft.Ref[ft.Dropdown]()
    selected_class = ft.Ref[ft.Dropdown]()
    selected_subject = ft.Ref[ft.Dropdown]()

    def toggle_stats_visibility(e):
        """Toggle statistics section visibility with smooth animation."""
        button = e.control
        if stats_visible.current.visible:
            stats_visible.current.visible = False
            stats_visible.current.height = 0
            button.icon = ft.Icons.EXPAND_MORE
            button.tooltip = "Afficher les statistiques"
        else:
            stats_visible.current.visible = True
            stats_visible.current.height = None
            button.icon = ft.Icons.EXPAND_LESS
            button.tooltip = "Masquer les statistiques"
        page.update()

    def toggle_attendance_stats(e):
        """Toggle attendance statistics visibility with smooth animation."""
        button = e.control
        if attendance_stats_visible.current.visible:
            attendance_stats_visible.current.visible = False
            attendance_stats_visible.current.height = 0
            button.icon = ft.Icons.EXPAND_MORE
        else:
            attendance_stats_visible.current.visible = True
            attendance_stats_visible.current.height = None
            button.icon = ft.Icons.EXPAND_LESS
            update_attendance_charts()
        page.update()

    def toggle_quiz_stats(e):
        """Toggle quiz statistics visibility with smooth animation."""
        button = e.control
        if quiz_stats_visible.current.visible:
            quiz_stats_visible.current.visible = False
            quiz_stats_visible.current.height = 0
            button.icon = ft.Icons.EXPAND_MORE
        else:
            quiz_stats_visible.current.visible = True
            quiz_stats_visible.current.height = None
            button.icon = ft.Icons.EXPAND_LESS
            update_quiz_charts()
        page.update()

    def toggle_data_tables(e):
        """Toggle data tables visibility with smooth animation."""
        button = e.control
        if data_tables_visible.current.visible:
            data_tables_visible.current.visible = False
            data_tables_visible.current.height = 0
            button.icon = ft.Icons.EXPAND_MORE
            button.tooltip = "Afficher les données"
        else:
            data_tables_visible.current.visible = True
            data_tables_visible.current.height = None
            button.icon = ft.Icons.EXPAND_LESS
            button.tooltip = "Masquer les données"
        page.update()

    def on_filter_change(_):
        """Handle filter changes and update visible charts."""
        if attendance_stats_visible.current.visible:
            update_attendance_charts()
        if quiz_stats_visible.current.visible:
            update_quiz_charts()

    def update_attendance_charts():
        """Update attendance charts based on filters."""
        days = int(selected_days.current.value) if selected_days.current.value else 30
        class_id = int(selected_class.current.value) if selected_class.current.value and selected_class.current.value != "all" else None
        subject_id = selected_subject.current.value if selected_subject.current.value and selected_subject.current.value != "all" else None

        # Get period label
        period_labels = {
            "7": "7 jours",
            "30": "30 jours",
            "90": "3 mois",
            "180": "6 mois",
            "365": "1 an"
        }
        period_label = period_labels.get(str(days), f"{days} jours")

        attendance_stats = get_attendance_statistics(days=days, class_id=class_id, subject_id=subject_id)

        # Update attendance charts container
        attendance_charts = [
            ft.Row([
                create_percentage_card(
                    "Taux de Présence",
                    attendance_stats['overall_stats'].get('overall_rate', 0),
                    attendance_stats['overall_stats'].get('total_present', 0),
                    ft.Icons.CHECK_CIRCLE,
                    ft.Colors.GREEN_600
                ),
                create_compact_stat_card(
                    "Total Absences",
                    attendance_stats['overall_stats'].get('total_absent', 0),
                    icon=ft.Icons.CANCEL,
                    color=ft.Colors.RED_600
                ),
                create_compact_stat_card(
                    "Sessions Totales",
                    attendance_stats['overall_stats'].get('total_records', 0),
                    icon=ft.Icons.EVENT,
                    color=ft.Colors.BLUE_600
                ),
            ], spacing=20, wrap=True, alignment=ft.MainAxisAlignment.CENTER),

            create_chart_container(
                "Évolution Quotidienne",
                create_attendance_line_chart(attendance_stats['daily_stats'], period_label),
                f"Période: {attendance_stats['date_range']['start']} - {attendance_stats['date_range']['end']}"
            ),
        ]

        attendance_stats_visible.current.content = ft.Column(attendance_charts, spacing=20)
        page.update()

    def update_quiz_charts():
        """Update quiz charts based on filters."""
        days = int(selected_days.current.value) if selected_days.current.value else 30
        class_id = int(selected_class.current.value) if selected_class.current.value and selected_class.current.value != "all" else None
        subject_id = selected_subject.current.value if selected_subject.current.value and selected_subject.current.value != "all" else None

        # Get period label
        period_labels = {
            "7": "7 jours",
            "30": "30 jours",
            "90": "3 mois",
            "180": "6 mois",
            "365": "1 an"
        }
        period_label = period_labels.get(str(days), f"{days} jours")

        quiz_stats = get_quiz_statistics(days=days, class_id=class_id, subject_id=subject_id)

        # Update quiz charts container
        quiz_charts = [
            ft.Row([
                create_percentage_card(
                    "Score Moyen",
                    quiz_stats['overall_stats'].get('overall_avg_score', 0),
                    quiz_stats['overall_stats'].get('total_submissions', 0),
                    ft.Icons.QUIZ,
                    ft.Colors.PURPLE_600
                ),
                create_compact_stat_card(
                    "Quiz Actifs",
                    quiz_stats['overall_stats'].get('total_quizzes', 0),
                    icon=ft.Icons.ASSIGNMENT,
                    color=ft.Colors.BLUE_600
                ),
                create_compact_stat_card(
                    "Étudiants Participants",
                    quiz_stats['overall_stats'].get('total_students', 0),
                    icon=ft.Icons.PEOPLE,
                    color=ft.Colors.ORANGE_600
                ),
            ], spacing=20, wrap=True, alignment=ft.MainAxisAlignment.CENTER),

            ft.Row([
                create_chart_container(
                    "Performance Quotidienne",
                    create_quiz_score_line_chart(quiz_stats['daily_stats'], period_label),
                    f"Période: {quiz_stats['date_range']['start']} - {quiz_stats['date_range']['end']}"
                ),
                create_chart_container(
                    "Distribution des Scores",
                    create_quiz_score_distribution_pie_chart(quiz_stats['score_distribution'])
                ),
            ], spacing=20, wrap=True),
        ]

        quiz_stats_visible.current.content = ft.Column(quiz_charts, spacing=20)
        page.update()

    def get_all_data():
        """Get all data from the database."""
        try:
            with sqlite3.connect(Config.get_db_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                data = {}

                # Get classes with teacher names
                cursor.execute("""
                    SELECT c.*, t.full_name as teacher_name
                    FROM classes c
                    LEFT JOIN users u ON c.teacher_id = u.id
                    LEFT JOIN teachers t ON u.id = t.user_id
                    ORDER BY c.name
                """)
                data['classes'] = [dict(row) for row in cursor.fetchall()]

                # Get students
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.created_at DESC, e.name
                """)
                students_rows = cursor.fetchall()
                data['students'] = [dict(row) for row in students_rows]
                print(f"DEBUG: Found {len(data['students'])} students in database")

                # Get subjects (remove duplicates by grouping similar subjects)
                cursor.execute("""
                    SELECT m.id, m.name, m.description, m.created_at,
                           GROUP_CONCAT(c.name, ', ') as class_name,
                           COUNT(DISTINCT m.class_id) as class_count
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    GROUP BY m.name, m.description
                    ORDER BY m.name
                """)
                subjects_raw = cursor.fetchall()

                # Process subjects to show consolidated view
                data['subjects'] = []
                for row in subjects_raw:
                    subject = dict(row)
                    # If subject appears in multiple classes, show count
                    if subject['class_count'] > 1:
                        subject['class_name'] = f"{subject['class_name']} ({subject['class_count']} classes)"
                    data['subjects'].append(subject)

                # Get quizzes
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                data['quizzes'] = [dict(row) for row in cursor.fetchall()]

                # Get attendance records
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 100
                """)
                data['attendance'] = [dict(row) for row in cursor.fetchall()]

                return data

        except Exception as e:
            print(f"❌ Failed to get data: {e}")
            return {}

    # Get all data
    all_data.current = get_all_data()

    def refresh_data():
        """Refresh all data from database and update the display."""
        print("🔄 Refreshing admin data...")
        fresh_data = get_all_data()
        all_data.current = fresh_data

        # Update data tables if visible
        if data_tables_visible.current and data_tables_visible.current.visible:
            update_data_tables()

        page.update()
        print("✅ Admin data refreshed successfully")

    def update_data_tables():
        """Update the data tables with fresh data."""
        if not data_tables_visible.current:
            return

        # Create fresh data sections
        fresh_data_sections = []
        fresh_data_sections.append(create_data_table("Classes", all_data.current.get('classes', []),
                             ['name', 'description', 'teacher_name', 'created_at'], ft.Icons.SCHOOL))
        fresh_data_sections.append(create_data_table("Étudiants", all_data.current.get('students', []),
                             ['name', 'class_name', 'created_at'], ft.Icons.PEOPLE))
        fresh_data_sections.append(create_data_table("Matières", all_data.current.get('subjects', []),
                             ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK))
        fresh_data_sections.append(create_data_table("Quiz", all_data.current.get('quizzes', []),
                             ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ))
        fresh_data_sections.append(create_data_table("Présences Récentes", all_data.current.get('attendance', []),
                             ['student_name', 'class_name', 'subject_name', 'date', 'status'], ft.Icons.ASSIGNMENT_TURNED_IN))

        # Update the container content
        data_tables_visible.current.content = ft.Column(fresh_data_sections, spacing=0)

    # Store refresh function for external access
    page._refresh_admin_data = refresh_data

    # Setup automatic refresh notifications
    try:
        from gui.services.notification_service import setup_admin_refresh_callback
        cleanup_callback = setup_admin_refresh_callback(page)

        # Store cleanup function for when page is destroyed
        page._cleanup_admin_notifications = lambda: None
        try:
            from gui.services.notification_service import unregister_callback
            page._cleanup_admin_notifications = lambda: unregister_callback('admin_data_refresh', cleanup_callback)
        except:
            pass

    except Exception as e:
        print(f"DEBUG: Failed to setup admin refresh notifications: {e}")

    # Modern welcome section with gradient background and refresh button
    def on_refresh_click(_):
        refresh_data()

    refresh_button = ft.IconButton(
        icon=ft.Icons.REFRESH,
        icon_color=ft.Colors.WHITE,
        tooltip="Actualiser les données",
        on_click=on_refresh_click,
        icon_size=24
    )

    # Add auto-refresh timer (every 30 seconds)
    def auto_refresh():
        """Auto-refresh data periodically."""
        import threading
        import time

        def refresh_loop():
            while True:
                try:
                    time.sleep(30)  # Wait 30 seconds
                    if hasattr(page, '_auto_refresh_enabled') and page._auto_refresh_enabled:
                        print("🔄 Auto-refreshing admin data...")
                        refresh_data()
                except Exception as e:
                    print(f"DEBUG: Auto-refresh error: {e}")
                    break

        # Start auto-refresh in background thread
        page._auto_refresh_enabled = True
        refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        refresh_thread.start()

    # Start auto-refresh
    auto_refresh()

    welcome_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(
                    ft.Icons.ANALYTICS,
                    size=32,
                    color=ft.Colors.WHITE
                ),
                ft.Text(
                    "Aperçu des Données Système",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE,
                    text_align=ft.TextAlign.CENTER
                ),
                refresh_button
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.Text(
                "Vue complète de toutes les données du système",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(
                content=ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Classes", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Matières", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Quiz", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    )
                ], spacing=16, alignment=ft.MainAxisAlignment.CENTER),
                margin=ft.margin.only(top=16)
            )
        ],
        spacing=12,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(32),
        margin=ft.margin.only(bottom=32, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(24),
        alignment=ft.alignment.center,
    )

    def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART):
        """Create a simple, big data table."""

        if not data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.INBOX,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        f"Aucune donnée {title.lower()}",
                        size=18,
                        color=ft.Colors.GREY_700,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        f"Les données {title.lower()} apparaîtront ici",
                        size=14,
                        color=ft.Colors.GREY_500,
                        text_align=ft.TextAlign.CENTER
                    )
                ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                padding=ft.padding.all(40),
                bgcolor=ft.Colors.WHITE,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.GREY_300),
                margin=ft.margin.only(bottom=24),
                width=1000
            )

        # Create simple table rows
        rows = []
        for i, item in enumerate(data[:50]):  # Limit to 50 items for performance
            cells = []
            for col in columns:
                value = str(item.get(col, ''))
                if len(value) > 40:
                    value = value[:37] + "..."

                cells.append(ft.DataCell(
                    ft.Text(
                        value,
                        size=15,
                        color=ft.Colors.GREY_800,
                        weight=ft.FontWeight.W_400
                    )
                ))

            # Simple alternate row colors
            row_color = ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
            rows.append(ft.DataRow(cells=cells, color=row_color))

        # Create simple table columns with French names
        column_names = {
            'name': 'Nom',
            'title': 'Titre',
            'description': 'Description',
            'class_name': 'Classe',
            'subject_name': 'Matière',
            'student_name': 'Étudiant',
            'teacher_name': 'Enseignant',
            'status': 'Statut',
            'created_at': 'Créé le',
            'date': 'Date',
            'time': 'Heure'
        }

        table_columns = []
        for col in columns:
            column_title = column_names.get(col, col.replace('_', ' ').title())
            table_columns.append(
                ft.DataColumn(
                    ft.Text(
                        column_title,
                        weight=ft.FontWeight.BOLD,
                        size=16,
                        color=ft.Colors.BLUE_900
                    )
                )
            )

        # Create simple, big table
        return ft.Container(
            content=ft.Column([
                # Simple title
                ft.Container(
                    content=ft.Row([
                        ft.Icon(icon, size=28, color=ft.Colors.BLUE_600),
                        ft.Text(
                            title,
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_900
                        ),
                    ], spacing=12),
                    margin=ft.margin.only(bottom=16)
                ),

                # Simple big data table
                ft.Container(
                    content=ft.DataTable(
                        columns=table_columns,
                        rows=rows,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=8,
                        vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        heading_row_color=ft.Colors.BLUE_50,
                        heading_row_height=56,
                        data_row_min_height=52,
                        data_row_max_height=52,
                        column_spacing=24,
                        horizontal_margin=16,
                        show_checkbox_column=False,
                    ),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=8,
                    padding=ft.padding.all(16),
                )
            ], spacing=0),
            width=1000,
            margin=ft.margin.only(bottom=24),
            alignment=ft.alignment.center
        )

    # Create data sections with modern design and French labels
    data_sections = []
    data_sections.append(create_data_table("Classes", all_data.current.get('classes', []),
                         ['name', 'description', 'teacher_name', 'created_at'], ft.Icons.SCHOOL))
    data_sections.append(create_data_table("Étudiants", all_data.current.get('students', []),
                         ['name', 'class_name', 'created_at'], ft.Icons.PEOPLE))
    data_sections.append(create_data_table("Matières", all_data.current.get('subjects', []),
                         ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK))
    data_sections.append(create_data_table("Quiz", all_data.current.get('quizzes', []),
                         ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ))
    data_sections.append(create_data_table("Présences Récentes", all_data.current.get('attendance', []),
                         ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'], ft.Icons.CHECK_CIRCLE))

    # Get filter options for statistics
    filter_options = get_filter_options()

    # Create optimized filter controls
    selected_days.current = ft.Dropdown(
        label="Période",
        value="30",
        options=[
            ft.dropdown.Option("7", "7j"),
            ft.dropdown.Option("30", "30j"),
            ft.dropdown.Option("90", "3m"),
            ft.dropdown.Option("180", "6m"),
            ft.dropdown.Option("365", "1an"),
        ],
        width=100,
        dense=True,
        on_change=on_filter_change,
    )

    selected_class.current = ft.Dropdown(
        label="Classe",
        value="all",
        options=[ft.dropdown.Option("all", "Toutes")] + [
            ft.dropdown.Option(str(cls['id']), cls['name'])
            for cls in filter_options['classes']
        ],
        width=120,
        dense=True,
        on_change=on_filter_change,
    )

    selected_subject.current = ft.Dropdown(
        label="Matière",
        value="all",
        options=[ft.dropdown.Option("all", "Toutes")] + [
            ft.dropdown.Option(str(subj['id']), subj['name'])
            for subj in filter_options['subjects']
        ],
        width=120,
        dense=True,
        on_change=on_filter_change,
    )

    # Create statistics section (without filters)
    stats_header = ft.Container(
        content=ft.Row([
            ft.Icon(ft.Icons.BAR_CHART, size=24, color=ft.Colors.BLUE_600),
            ft.Text(
                "Statistiques",
                size=20,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800,
            ),
            ft.Container(expand=True),
            ft.IconButton(
                icon=ft.Icons.EXPAND_MORE,
                tooltip="Afficher/Masquer les statistiques",
                on_click=toggle_stats_visibility,
                icon_color=ft.Colors.BLUE_600,
            ),
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        margin=ft.margin.only(bottom=20),
    )

    # Create expandable statistics containers
    attendance_stats_visible.current = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Text(
                    "Cliquez pour afficher les statistiques de présence",
                    text_align=ft.TextAlign.CENTER,
                    color=ft.Colors.GREY_500,
                    size=14,
                ),
                padding=ft.padding.all(20),
            )
        ]),
        visible=False,
        height=0,
        animate=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
        animate_size=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
    )

    quiz_stats_visible.current = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Text(
                    "Cliquez pour afficher les statistiques de quiz",
                    text_align=ft.TextAlign.CENTER,
                    color=ft.Colors.GREY_500,
                    size=14,
                ),
                padding=ft.padding.all(20),
            )
        ]),
        visible=False,
        height=0,
        animate=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
        animate_size=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
    )

    stats_visible.current = ft.Container(
        content=ft.Column([
            # Attendance section with filters
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.CHECK_CIRCLE, size=20, color=ft.Colors.GREEN_600),
                        ft.Text(
                            "Statistiques de Présence",
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.GREY_700,
                        ),
                        ft.Container(expand=True),
                        ft.IconButton(
                            icon=ft.Icons.EXPAND_MORE,
                            tooltip="Afficher les statistiques de présence",
                            on_click=toggle_attendance_stats,
                            icon_color=ft.Colors.GREEN_600,
                            icon_size=20,
                            style=ft.ButtonStyle(
                                shape=ft.CircleBorder(),
                                bgcolor=ft.Colors.with_opacity(0.1, ft.Colors.GREEN_600),
                            ),
                        ),
                    ]),
                    # Filter controls for attendance statistics
                    ft.Container(
                        content=ft.Row([
                            ft.Text(
                                "Filtres:",
                                size=12,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_600,
                            ),
                            selected_days.current,
                            selected_class.current,
                            selected_subject.current,
                        ], spacing=10, alignment=ft.MainAxisAlignment.START),
                        margin=ft.margin.only(top=10),
                    ),
                ], spacing=0),
                bgcolor=ft.Colors.with_opacity(0.03, ft.Colors.GREEN_600),
                border_radius=ft.border_radius.all(12),
                padding=ft.padding.all(16),
                margin=ft.margin.only(bottom=10),
                border=ft.border.all(1, ft.Colors.with_opacity(0.2, ft.Colors.GREEN_600)),
            ),
            attendance_stats_visible.current,

            # Quiz section
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.QUIZ, size=20, color=ft.Colors.PURPLE_600),
                    ft.Text(
                        "Statistiques de Quiz",
                        size=16,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.GREY_700,
                    ),
                    ft.Container(expand=True),
                    ft.IconButton(
                        icon=ft.Icons.EXPAND_MORE,
                        tooltip="Afficher les statistiques de quiz",
                        on_click=toggle_quiz_stats,
                        icon_color=ft.Colors.PURPLE_600,
                        icon_size=20,
                        style=ft.ButtonStyle(
                            shape=ft.CircleBorder(),
                            bgcolor=ft.Colors.with_opacity(0.1, ft.Colors.PURPLE_600),
                        ),
                    ),
                ]),
                bgcolor=ft.Colors.with_opacity(0.03, ft.Colors.PURPLE_600),
                border_radius=ft.border_radius.all(12),
                padding=ft.padding.all(16),
                margin=ft.margin.only(bottom=10),
                border=ft.border.all(1, ft.Colors.with_opacity(0.2, ft.Colors.PURPLE_600)),
            ),
            quiz_stats_visible.current,
        ], spacing=15),
        visible=False,
        height=0,
        animate=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
        animate_size=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
        bgcolor=ft.Colors.with_opacity(0.02, ft.Colors.BLUE_600),
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
    )

    # Create data tables header
    data_tables_header = ft.Container(
        content=ft.Row([
            ft.Icon(ft.Icons.TABLE_VIEW, size=24, color=ft.Colors.GREEN_600),
            ft.Text(
                "Toutes les Données",
                size=20,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800,
            ),
            ft.Container(expand=True),
            ft.IconButton(
                icon=ft.Icons.EXPAND_MORE,
                tooltip="Afficher les données",
                on_click=toggle_data_tables,
                icon_color=ft.Colors.GREEN_600,
            ),
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        margin=ft.margin.only(bottom=20),
    )

    # Create expandable data tables container
    data_tables_visible.current = ft.Container(
        content=ft.Column([
            ft.Container(
                content=section,
                alignment=ft.alignment.center,
            ) for section in data_sections
        ], spacing=0),
        visible=False,
        height=0,
        animate=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
        animate_size=ft.Animation(400, ft.AnimationCurve.EASE_IN_OUT),
    )

    # Create enhanced content
    content = [
        welcome_section,
        stats_header,
        stats_visible.current,
        data_tables_header,
        data_tables_visible.current,
    ]

    return create_admin_page_layout(
        page,
        "Aperçu des Données",
        content
    )
