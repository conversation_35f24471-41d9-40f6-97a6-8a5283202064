"""
Matplotlib chart service for generating professional-looking charts.
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime
import os
import io
import base64

# Set modern style for better-looking charts
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif'],
    'font.size': 11,
    'axes.titlesize': 16,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 11,
    'figure.titlesize': 18,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linewidth': 0.8,
    'axes.edgecolor': '#CCCCCC',
    'axes.linewidth': 1.2,
    'figure.facecolor': 'white',
    'axes.facecolor': '#FAFAFA'
})

# Modern color palette
COLORS = {
    'primary': '#2E86AB',      # Blue
    'secondary': '#A23B72',    # Purple
    'success': '#F18F01',      # Orange
    'warning': '#C73E1D',      # Red
    'info': '#4ECDC4',         # Teal
    'light': '#F8F9FA',        # Light gray
    'dark': '#343A40',         # Dark gray
    'gradient_start': '#667eea',
    'gradient_end': '#764ba2'
}

# Create charts directory if it doesn't exist
CHARTS_DIR = "gui/static/charts"
os.makedirs(CHARTS_DIR, exist_ok=True)


def configure_date_axis(ax, dates):
    """Configure date axis with appropriate tick spacing to prevent overflow."""
    if not dates:
        return

    try:
        # Set axis limits first
        min_date = min(dates)
        max_date = max(dates)
        ax.set_xlim(min_date, max_date)

        # Configure ticks based on date range
        date_range = (max_date - min_date).days

        # Use safer locators with explicit limits
        if date_range > 365:  # More than a year
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%y'))
        elif date_range > 90:  # More than 3 months
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        elif date_range > 30:  # More than a month
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        elif date_range > 14:  # More than 2 weeks
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        elif date_range > 7:   # More than a week
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        else:  # A week or less
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))

        # Limit maximum number of ticks to prevent overflow
        try:
            ax.locator_params(axis='x', max_nbins=8)
        except:
            # Some locators don't support max_nbins parameter
            pass

    except Exception as e:
        # Fallback to simple formatting if date configuration fails
        print(f"Warning: Date axis configuration failed: {e}")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        try:
            ax.locator_params(axis='x', max_nbins=6)
        except:
            pass


def create_attendance_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional attendance line chart using matplotlib.

    Args:
        daily_stats: List of daily attendance statistics
        period_label: Label for the time period

    Returns:
        str: Base64 encoded image data
    """
    if not daily_stats:
        return create_no_data_chart("Aucune donnée de présence disponible")

    # Prepare data
    dates = []
    rates = []

    for stat in daily_stats:
        try:
            date_str = stat.get('date', '')
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                rates.append(stat.get('attendance_rate', 0))
        except:
            continue

    if not dates:
        return create_no_data_chart("Aucune donnée de présence disponible")

    # Create figure with modern styling
    fig, ax = plt.subplots(figsize=(14, 7))
    fig.patch.set_facecolor('white')

    # Create simple gradient background without date dependency
    ax.set_facecolor('#FAFAFA')
    # Add a subtle color gradient as background
    gradient = np.linspace(0, 1, 100).reshape(1, -1)
    ax.imshow(gradient, extent=[0, 1, 0, 1], aspect='auto', alpha=0.05,
              cmap='Blues', transform=ax.transAxes, zorder=0)

    # Plot main line with shadow effect
    ax.plot(dates, rates, linewidth=4, color=COLORS['primary'],
            alpha=0.8, zorder=3, label='Taux de présence')

    # Add shadow line
    ax.plot(dates, rates, linewidth=6, color=COLORS['primary'],
            alpha=0.2, zorder=2)

    # Plot points with modern styling
    ax.scatter(dates, rates, s=80, color=COLORS['primary'],
               edgecolors='white', linewidth=2, zorder=4, alpha=0.9)

    # Fill area under curve with gradient
    ax.fill_between(dates, rates, alpha=0.2, color=COLORS['primary'])

    # Customize title
    ax.set_title(f'Évolution du Taux de Présence ({period_label})',
                fontsize=18, fontweight='bold', pad=25, color=COLORS['dark'])

    # Style axes labels
    ax.set_xlabel('Période', fontsize=13, fontweight='600', color=COLORS['dark'])
    ax.set_ylabel('Taux de Présence (%)', fontsize=13, fontweight='600', color=COLORS['dark'])

    # Configure date axis safely
    configure_date_axis(ax, dates)

    # Enhanced y-axis
    ax.set_ylim(-2, 102)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{int(x)}%'))

    # Modern grid styling
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.8, color='#E0E0E0')
    ax.set_axisbelow(True)

    # Add average line
    avg_rate = np.mean(rates)
    ax.axhline(y=avg_rate, color=COLORS['warning'], linestyle='--',
               linewidth=2, alpha=0.7, label=f'Moyenne: {avg_rate:.1f}%')

    # Style spines
    for spine in ax.spines.values():
        spine.set_color('#CCCCCC')
        spine.set_linewidth(1)

    # Add legend with modern styling
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                      shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('#CCCCCC')

    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha='right')

    # Tight layout with padding
    plt.tight_layout(pad=2.0)

    # Convert to base64
    return fig_to_base64(fig)


def create_attendance_bar_chart(class_stats):
    """
    Create a professional attendance bar chart by class.

    Args:
        class_stats: List of class attendance statistics

    Returns:
        str: Base64 encoded image data
    """
    if not class_stats:
        return create_no_data_chart("Aucune donnée par classe disponible")

    # Limit to top 10 classes for readability
    class_stats = class_stats[:10]

    # Prepare data
    class_names = [stat.get('class_name', f'Classe {i+1}')[:15] for i, stat in enumerate(class_stats)]
    rates = [stat.get('attendance_rate', 0) for stat in class_stats]

    # Create figure with modern styling
    fig, ax = plt.subplots(figsize=(14, 8))
    fig.patch.set_facecolor('white')

    # Create gradient colors based on performance
    colors = []
    for rate in rates:
        if rate >= 90:
            colors.append(COLORS['success'])
        elif rate >= 75:
            colors.append(COLORS['primary'])
        elif rate >= 60:
            colors.append(COLORS['info'])
        else:
            colors.append(COLORS['warning'])

    # Create bars with modern styling
    bars = ax.bar(class_names, rates, color=colors, alpha=0.8,
                  edgecolor='white', linewidth=2, width=0.7)

    # Add gradient effect to bars
    for bar, rate in zip(bars, rates):
        # Add subtle gradient effect
        gradient = np.linspace(0, 1, 100).reshape(100, 1)
        ax.imshow(gradient, extent=[bar.get_x(), bar.get_x() + bar.get_width(),
                                   0, rate], aspect='auto', alpha=0.1,
                  cmap='Blues', zorder=1)

    # Add value labels on bars with modern styling
    for bar, rate in zip(bars, rates):
        height = bar.get_height()
        # Add background circle for label
        circle = plt.Circle((bar.get_x() + bar.get_width()/2., height + 3),
                           radius=2, color='white', alpha=0.9, zorder=5)
        ax.add_patch(circle)

        ax.text(bar.get_x() + bar.get_width()/2., height + 3,
                f'{rate:.1f}%', ha='center', va='center',
                fontweight='bold', fontsize=11, color=COLORS['dark'], zorder=6)

    # Modern title
    ax.set_title('Taux de Présence par Classe',
                fontsize=18, fontweight='bold', pad=25, color=COLORS['dark'])

    # Style axes labels
    ax.set_xlabel('Classes', fontsize=13, fontweight='600', color=COLORS['dark'])
    ax.set_ylabel('Taux de Présence (%)', fontsize=13, fontweight='600', color=COLORS['dark'])

    # Enhanced y-axis
    ax.set_ylim(0, max(rates) + 15 if rates else 100)
    y_max = max(rates) + 15 if rates else 100
    ax.set_yticks(range(0, int(y_max) + 1, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{int(x)}%'))

    # Modern grid styling
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.8,
            color='#E0E0E0', axis='y')
    ax.set_axisbelow(True)

    # Add average line
    if rates:
        avg_rate = np.mean(rates)
        ax.axhline(y=avg_rate, color=COLORS['secondary'], linestyle='--',
                   linewidth=2, alpha=0.7, label=f'Moyenne: {avg_rate:.1f}%')

        # Add legend
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                          shadow=True, framealpha=0.9)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_edgecolor('#CCCCCC')

    # Style spines
    for spine in ax.spines.values():
        spine.set_color('#CCCCCC')
        spine.set_linewidth(1)

    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha='right', fontsize=10)

    # Tight layout with padding
    plt.tight_layout(pad=2.0)

    return fig_to_base64(fig)


def create_attendance_pie_chart(overall_stats):
    """
    Create a professional attendance pie chart.

    Args:
        overall_stats: Overall attendance statistics

    Returns:
        str: Base64 encoded image data
    """
    total_present = overall_stats.get('total_present', 0)
    total_absent = overall_stats.get('total_absent', 0)

    if total_present == 0 and total_absent == 0:
        return create_no_data_chart("Aucune donnée de présence disponible")

    # Prepare data
    labels = []
    sizes = []
    colors = []
    explode = []

    if total_present > 0:
        labels.append(f'Présent\n{total_present} étudiants')
        sizes.append(total_present)
        colors.append(COLORS['success'])
        explode.append(0.05)  # Slightly separate the slice

    if total_absent > 0:
        labels.append(f'Absent\n{total_absent} étudiants')
        sizes.append(total_absent)
        colors.append(COLORS['warning'])
        explode.append(0.05)

    # Create figure with modern styling
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor('white')

    # Create modern pie chart with enhanced styling
    _, _, autotexts = ax.pie(
        sizes,
        labels=labels,
        colors=colors,
        autopct='%1.1f%%',
        startangle=90,
        explode=explode,
        shadow=True,
        textprops={'fontsize': 12, 'fontweight': 'bold', 'color': COLORS['dark']},
        wedgeprops={'linewidth': 3, 'edgecolor': 'white', 'alpha': 0.9}
    )

    # Enhance autopct text styling
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(14)
        autotext.set_fontweight('bold')

    # Modern title
    ax.set_title('Répartition Présence/Absence',
                fontsize=18, fontweight='bold', pad=25, color=COLORS['dark'])

    # Add center circle for donut effect
    centre_circle = plt.Circle((0, 0), 0.40, fc='white', linewidth=2, edgecolor=COLORS['primary'])
    ax.add_artist(centre_circle)

    # Add center text with statistics
    total = total_present + total_absent
    if total > 0:
        attendance_rate = (total_present / total) * 100
        ax.text(0, 0.1, f'{attendance_rate:.1f}%', ha='center', va='center',
                fontsize=24, fontweight='bold', color=COLORS['primary'])
        ax.text(0, -0.1, 'Taux de\nPrésence', ha='center', va='center',
                fontsize=12, fontweight='600', color=COLORS['dark'])

    # Equal aspect ratio ensures that pie is drawn as a circle
    ax.axis('equal')

    # Add subtle background
    ax.set_facecolor('#FAFAFA')

    # Tight layout with padding
    plt.tight_layout(pad=2.0)

    return fig_to_base64(fig)


def create_quiz_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional quiz performance line chart.
    
    Args:
        daily_stats: List of daily quiz statistics
        period_label: Label for the time period
        
    Returns:
        str: Base64 encoded image data
    """
    if not daily_stats:
        return create_no_data_chart("Aucune donnée de quiz disponible")
    
    # Prepare data
    dates = []
    avg_scores = []
    
    for stat in daily_stats:
        try:
            date_str = stat.get('date', '')
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                avg_scores.append(stat.get('avg_score', 0))
        except:
            continue
    
    if not dates:
        return create_no_data_chart("Aucune donnée de quiz disponible")
    
    # Create figure with modern styling
    fig, ax = plt.subplots(figsize=(14, 7))
    fig.patch.set_facecolor('white')

    # Create simple gradient background without date dependency
    ax.set_facecolor('#FAFAFA')
    # Add a subtle color gradient as background
    gradient = np.linspace(0, 1, 100).reshape(1, -1)
    ax.imshow(gradient, extent=[0, 1, 0, 1], aspect='auto', alpha=0.05,
              cmap='Purples', transform=ax.transAxes, zorder=0)

    # Plot main line with shadow effect
    ax.plot(dates, avg_scores, linewidth=4, color=COLORS['secondary'],
            alpha=0.8, zorder=3, label='Score moyen')

    # Add shadow line
    ax.plot(dates, avg_scores, linewidth=6, color=COLORS['secondary'],
            alpha=0.2, zorder=2)

    # Plot points with modern styling
    ax.scatter(dates, avg_scores, s=100, color=COLORS['secondary'],
               edgecolors='white', linewidth=2, zorder=4, alpha=0.9, marker='D')

    # Fill area under curve
    ax.fill_between(dates, avg_scores, alpha=0.15, color=COLORS['secondary'])

    # Modern title
    ax.set_title(f'Évolution des Scores de Quiz ({period_label})',
                fontsize=18, fontweight='bold', pad=25, color=COLORS['dark'])

    # Style axes labels
    ax.set_xlabel('Période', fontsize=13, fontweight='600', color=COLORS['dark'])
    ax.set_ylabel('Score Moyen (%)', fontsize=13, fontweight='600', color=COLORS['dark'])

    # Configure date axis safely
    configure_date_axis(ax, dates)

    # Enhanced y-axis
    ax.set_ylim(-2, 102)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{int(x)}%'))

    # Modern grid styling
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.8, color='#E0E0E0')
    ax.set_axisbelow(True)

    # Add average line
    if avg_scores:
        overall_avg = np.mean(avg_scores)
        ax.axhline(y=overall_avg, color=COLORS['info'], linestyle='--',
                   linewidth=2, alpha=0.7, label=f'Moyenne globale: {overall_avg:.1f}%')

    # Style spines
    for spine in ax.spines.values():
        spine.set_color('#CCCCCC')
        spine.set_linewidth(1)

    # Add legend with modern styling
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                      shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('#CCCCCC')

    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha='right')

    # Tight layout with padding
    plt.tight_layout(pad=2.0)

    return fig_to_base64(fig)


def create_quiz_bar_chart(class_stats):
    """
    Create a professional quiz performance bar chart by class.
    
    Args:
        class_stats: List of class quiz statistics
        
    Returns:
        str: Base64 encoded image data
    """
    if not class_stats:
        return create_no_data_chart("Aucune donnée de quiz par classe disponible")
    
    # Limit to top 8 classes for readability
    class_stats = class_stats[:8]
    
    # Prepare data
    class_names = [stat.get('class_name', f'Classe {i+1}')[:15] for i, stat in enumerate(class_stats)]
    scores = [stat.get('avg_score', 0) for stat in class_stats]
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Create bars with gradient colors
    colors = plt.cm.plasma(np.linspace(0.3, 0.9, len(class_names)))
    bars = ax.bar(class_names, scores, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
    
    # Add value labels on bars
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{score:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Customize axes
    ax.set_title('Score Moyen par Classe', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Classes', fontsize=12, fontweight='bold')
    ax.set_ylabel('Score Moyen (%)', fontsize=12, fontweight='bold')
    
    # Set y-axis limits and ticks
    ax.set_ylim(0, 100)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{int(x)}%'))
    
    # Add grid
    ax.grid(True, alpha=0.3, axis='y')
    
    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right')
    
    # Tight layout
    plt.tight_layout()
    
    return fig_to_base64(fig)


def create_quiz_pie_chart(score_distribution):
    """
    Create a professional quiz score distribution pie chart.

    Args:
        score_distribution: List of score distribution data

    Returns:
        str: Base64 encoded image data
    """
    if not score_distribution:
        return create_no_data_chart("Aucune donnée de distribution disponible")

    # Prepare data with modern colors
    labels = []
    sizes = []
    colors = [COLORS['warning'], COLORS['info'], COLORS['primary'], COLORS['success'], '#FFD700']
    explode = []

    for dist in score_distribution:
        score_range = dist.get('score_range', '')
        count = dist.get('count', 0)

        if count > 0:
            # Add simple text labels
            labels.append(f'{score_range}\n{count} étudiants')
            sizes.append(count)
            explode.append(0.05)  # Slightly separate each slice

    if not sizes:
        return create_no_data_chart("Aucune donnée de distribution disponible")

    # Create figure with modern styling
    fig, ax = plt.subplots(figsize=(12, 9))
    fig.patch.set_facecolor('white')

    # Create modern pie chart with enhanced styling
    _, _, autotexts = ax.pie(
        sizes,
        labels=labels,
        colors=colors[:len(sizes)],
        autopct='%1.1f%%',
        startangle=90,
        explode=explode,
        shadow=True,
        textprops={'fontsize': 12, 'fontweight': 'bold', 'color': COLORS['dark']},
        wedgeprops={'linewidth': 3, 'edgecolor': 'white', 'alpha': 0.9}
    )

    # Enhance autopct text styling
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(13)
        autotext.set_fontweight('bold')

    # Modern title
    ax.set_title('Distribution des Scores de Quiz',
                fontsize=18, fontweight='bold', pad=25, color=COLORS['dark'])

    # Add center circle for donut effect
    centre_circle = plt.Circle((0, 0), 0.35, fc='white', linewidth=2, edgecolor=COLORS['secondary'])
    ax.add_artist(centre_circle)

    # Add center text with total students
    total_students = sum(sizes)
    ax.text(0, 0.1, f'{total_students}', ha='center', va='center',
            fontsize=24, fontweight='bold', color=COLORS['secondary'])
    ax.text(0, -0.1, 'Total\nÉtudiants', ha='center', va='center',
            fontsize=12, fontweight='600', color=COLORS['dark'])

    # Equal aspect ratio ensures that pie is drawn as a circle
    ax.axis('equal')

    # Add subtle background
    ax.set_facecolor('#FAFAFA')

    # Tight layout with padding
    plt.tight_layout(pad=2.0)

    return fig_to_base64(fig)


def fig_to_base64(fig):
    """Convert matplotlib figure to base64 string with high quality."""
    buffer = io.BytesIO()
    fig.savefig(buffer, format='png', dpi=200, bbox_inches='tight',
                facecolor='white', edgecolor='none',
                pad_inches=0.2, transparent=False,
                metadata={'Software': 'Teacher Assistant'})
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close(fig)
    return f"data:image/png;base64,{image_base64}"


def create_no_data_chart(message):
    """Create a modern chart showing no data message."""
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.patch.set_facecolor('white')

    # Add a subtle gradient background
    gradient = np.linspace(0, 1, 256).reshape(1, -1)
    ax.imshow(gradient, extent=[0, 1, 0, 1], aspect='auto', alpha=0.1, cmap='Blues')

    # Create modern message box
    bbox_props = dict(
        boxstyle="round,pad=0.5",
        facecolor=COLORS['light'],
        edgecolor=COLORS['primary'],
        linewidth=2,
        alpha=0.9
    )

    # Add message
    ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=16,
            transform=ax.transAxes, bbox=bbox_props,
            color=COLORS['dark'], fontweight='600')

    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    ax.set_facecolor('#FAFAFA')

    plt.tight_layout()
    return fig_to_base64(fig)
