#!/usr/bin/env python3
import os

# Remove GTK modules to avoid conflicts
os.environ.pop("GTK_MODULES", None)

# Configure CUDA/GPU settings based on deployment target
# For development: Force CPU-only mode to avoid CUDA issues
# For Jetson Nano: Allow CUDA usage by commenting out these lines
if not os.path.exists('/proc/device-tree/model') or 'jetson' not in open('/proc/device-tree/model', 'rb').read().decode('utf-8', errors='ignore').lower():
    # Not on Jetson - force CPU mode for development
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    os.environ['DLIB_USE_CUDA'] = '0'
    print("DEBUG: Development environment detected - using CPU-only mode")
else:
    # On Jetson - allow CUDA usage
    print("DEBUG: Jetson environment detected - CUDA enabled")

import flet as ft
import socket
import logging
from gui.app import main
from gui.utils.connection import check_connection
from gui.utils.network import get_local_ip

from quiz_management.services.quiz_service import initialize_quiz_tables
from facial_recognition_system.local_database import initialize_database
from facial_recognition_system.config import Config

logging.getLogger("flet").setLevel(logging.WARNING)
logging.getLogger("flet_desktop").setLevel(logging.WARNING)

def main_with_connection_check(page: ft.Page):
    # Monitor for page connections to switch from QR to face display
    from gui.services.connection_monitor import on_page_connected
    on_page_connected(page)

    main(page)
    page.check_connection = lambda: check_connection(page)


def find_available_port(start_port=None, max_attempts=5):
    """Find available port quickly with reduced attempts."""
    if start_port is None:
        start_port = Config.DEFAULT_PORT

    # Reserved ports to skip
    reserved_ports = {Config.ENROLLMENT_PORT, Config.VIDEO_STREAM_PORT, Config.QUIZ_PORT}

    for port in range(start_port, start_port + max_attempts):
        if port in reserved_ports:
            continue
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.settimeout(0.1)  # Quick timeout for port binding
                s.bind((Config.HOST, port))
                return port
            except OSError:
                continue

    # If no port found in range, try system-assigned port
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind((Config.HOST, 0))  # Let system choose port
            port = s.getsockname()[1]
            if port not in reserved_ports:
                return port
        except OSError:
            pass
    return 0


def run_app():
    import os
    import threading
    import time

    startup_time = time.time()
    print("🚀 Starting Teacher Assistant application...")

    # Initialize database tables in background to avoid blocking server startup
    def init_db_async():
        try:
            db_start = time.time()
            print("🔧 Initializing database tables...")
            # Initialize main database tables (classes, etudiants, matieres, presences)
            initialize_database()
            # Initialize quiz tables (quiz, questions_quiz, options_quiz, soumissions_quiz, reponses_quiz)
            initialize_quiz_tables()
            db_time = time.time() - db_start
            print(f"✅ Database tables initialized successfully ({db_time:.2f}s)")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")

    # Start database initialization in background thread
    db_init_thread = threading.Thread(target=init_db_async, daemon=True)
    db_init_thread.start()

    # Find available port quickly
    port_start = time.time()
    port = find_available_port()
    port_time = time.time() - port_start
    print(f"🔌 Port {port} found ({port_time:.2f}s)")

    # Get IP address with timeout
    ip_start = time.time()
    ip_address = get_local_ip()
    ip_time = time.time() - ip_start
    print(f"🌐 IP address {ip_address} detected ({ip_time:.2f}s)")

    os.environ['FLET_PORT'] = str(port)

    # Show QR code first with the IP address
    from gui.services.qr_display_service import start_qr_display, switch_to_face_display, setup_pygame_environment
    from gui.services.connection_monitor import start_flet_monitoring

    # Setup pygame environment to avoid GLX errors
    setup_pygame_environment()

    print("📱 Starting QR code display...")
    qr_url = f"http://{ip_address}:{port}"
    success = start_qr_display(qr_url)
    if success:
        print(f"✅ QR code displayed for: {qr_url}")
    else:
        print("❌ QR code display failed")

    # Start monitoring for connections to switch to face display
    print("🔍 Starting connection monitor...")
    start_flet_monitoring(switch_to_face_display)

    total_startup_time = time.time() - startup_time
    print("\n" + "="*60)
    print(f"{Config.APP_NAME} v{Config.VERSION} is running!")
    print("="*60)
    print(f"Local access:     http://localhost:{port}")
    print(f"Network access:   http://{ip_address}:{port}")
    print(f"Startup time:     {total_startup_time:.2f}s")
    print("="*60 + "\n")

    ft.app(
        target=main_with_connection_check,
        port=port,
        host="0.0.0.0",
        upload_dir=str(Config.UPLOADS_DIR),
        view=None,
        # view=ft.AppView.WEB_BROWSER,
    )

if __name__ == "__main__":
    run_app()
